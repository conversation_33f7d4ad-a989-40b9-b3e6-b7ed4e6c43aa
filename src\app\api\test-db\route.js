import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET() {
  try {
    // Test database connection
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('*')
      .limit(5)

    if (categoriesError) {
      return NextResponse.json({
        success: false,
        error: 'Database connection failed',
        details: categoriesError.message
      }, { status: 500 })
    }

    const { data: subjects, error: subjectsError } = await supabase
      .from('subjects')
      .select('*')
      .limit(5)

    if (subjectsError) {
      return NextResponse.json({
        success: false,
        error: 'Subjects table not accessible',
        details: subjectsError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      data: {
        categories: categories.length,
        subjects: subjects.length,
        sampleCategory: categories[0]?.name || 'No categories found',
        sampleSubject: subjects[0]?.name || 'No subjects found'
      }
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Server error',
      details: error.message
    }, { status: 500 })
  }
}
