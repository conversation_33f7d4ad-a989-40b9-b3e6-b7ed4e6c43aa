{"name": "notex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup-supabase": "node scripts/setup-supabase.js"}, "dependencies": {"@headlessui/react": "^2.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.536.0", "mammoth": "^1.9.1", "next": "15.4.5", "pdf-lib": "^1.17.1", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hot-toast": "^2.5.2", "react-pdf": "^10.0.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4"}}