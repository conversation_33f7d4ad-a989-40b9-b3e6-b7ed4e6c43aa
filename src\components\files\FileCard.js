'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  Download, 
  Eye, 
  Heart, 
  MessageCircle, 
  Star,
  FileText,
  Image,
  File
} from 'lucide-react'
import { formatFileSize, formatDate } from '@/lib/utils'
import { useAuth } from '@/contexts/AuthContext'

const getFileIcon = (fileType) => {
  switch (fileType.toLowerCase()) {
    case 'pdf':
      return <FileText className="h-6 w-6 text-red-500" />
    case 'docx':
    case 'doc':
      return <FileText className="h-6 w-6 text-blue-500" />
    case 'txt':
      return <FileText className="h-6 w-6 text-gray-500" />
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return <Image className="h-6 w-6 text-green-500" />
    default:
      return <File className="h-6 w-6 text-gray-500" />
  }
}

export default function FileCard({ file, onBookmark, onRate }) {
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [rating, setRating] = useState(0)
  const { user } = useAuth()

  const handleBookmark = async () => {
    if (!user) return
    
    try {
      const bookmarked = await onBookmark?.(file.id)
      setIsBookmarked(bookmarked)
    } catch (error) {
      console.error('Error bookmarking file:', error)
    }
  }

  const handleRate = async (newRating) => {
    if (!user) return
    
    try {
      await onRate?.(file.id, newRating)
      setRating(newRating)
    } catch (error) {
      console.error('Error rating file:', error)
    }
  }

  const averageRating = file.ratings?.length > 0 
    ? file.ratings.reduce((sum, r) => sum + r.rating, 0) / file.ratings.length 
    : 0

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
      {/* File Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            {getFileIcon(file.file_type)}
            <div className="flex-1 min-w-0">
              <Link 
                href={`/files/${file.id}`}
                className="block"
              >
                <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors truncate">
                  {file.title}
                </h3>
              </Link>
              <p className="text-sm text-gray-500 mt-1">
                {formatFileSize(file.file_size)} • {file.file_type.toUpperCase()}
              </p>
            </div>
          </div>
          
          {user && (
            <button
              onClick={handleBookmark}
              className={`p-2 rounded-lg transition-colors ${
                isBookmarked 
                  ? 'text-red-500 bg-red-50 hover:bg-red-100' 
                  : 'text-gray-400 hover:text-red-500 hover:bg-gray-50'
              }`}
            >
              <Heart className={`h-5 w-5 ${isBookmarked ? 'fill-current' : ''}`} />
            </button>
          )}
        </div>
      </div>

      {/* File Content */}
      <div className="p-4">
        {file.description && (
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {file.description}
          </p>
        )}

        {/* Metadata */}
        <div className="space-y-2 mb-4">
          {file.subject && (
            <div className="flex items-center text-sm">
              <span className="text-gray-500">Subject:</span>
              <span className="ml-2 text-gray-900">{file.subject.name}</span>
            </div>
          )}
          
          {file.author && (
            <div className="flex items-center text-sm">
              <span className="text-gray-500">Author:</span>
              <span className="ml-2 text-gray-900">{file.author}</span>
            </div>
          )}
          
          {file.course_name && (
            <div className="flex items-center text-sm">
              <span className="text-gray-500">Course:</span>
              <span className="ml-2 text-gray-900">{file.course_name}</span>
            </div>
          )}
        </div>

        {/* Category Badge */}
        {file.category && (
          <div className="mb-4">
            <span 
              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              style={{ 
                backgroundColor: `${file.category.color}20`,
                color: file.category.color 
              }}
            >
              {file.category.name}
            </span>
          </div>
        )}

        {/* Rating */}
        <div className="flex items-center space-x-2 mb-4">
          <div className="flex items-center">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                onClick={() => handleRate(star)}
                disabled={!user}
                className={`h-4 w-4 ${
                  star <= (rating || averageRating)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                } ${user ? 'hover:text-yellow-400 cursor-pointer' : 'cursor-default'}`}
              >
                <Star className="h-4 w-4" />
              </button>
            ))}
          </div>
          <span className="text-sm text-gray-500">
            ({file.ratings?.length || 0} reviews)
          </span>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Eye className="h-4 w-4" />
              <span>{file.view_count || 0}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Download className="h-4 w-4" />
              <span>{file.download_count || 0}</span>
            </div>
            <div className="flex items-center space-x-1">
              <MessageCircle className="h-4 w-4" />
              <span>{file._count?.comments || 0}</span>
            </div>
          </div>
          <span>{formatDate(file.created_at)}</span>
        </div>

        {/* Uploader Info */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-xs font-medium text-gray-600">
                {file.uploaded_by?.full_name?.charAt(0) || 'U'}
              </span>
            </div>
            <span className="text-sm text-gray-600">
              {file.uploaded_by?.full_name || 'Anonymous'}
            </span>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
        <Link
          href={`/files/${file.id}`}
          className="text-blue-600 hover:text-blue-700 text-sm font-medium"
        >
          View Details
        </Link>
        
        <div className="flex items-center space-x-2">
          <Link
            href={`/files/${file.id}/preview`}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <Eye className="h-4 w-4 mr-1" />
            Preview
          </Link>
          
          <Link
            href={`/api/files/${file.id}/download`}
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <Download className="h-4 w-4 mr-1" />
            Download
          </Link>
        </div>
      </div>
    </div>
  )
}
