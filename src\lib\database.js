import { supabase, supabaseAdmin } from './supabase'

// User Profile Functions
export async function getUserProfile(userId) {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()
  
  if (error) throw error
  return data
}

export async function updateUserProfile(userId, updates) {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()
  
  if (error) throw error
  return data
}

// Category Functions
export async function getCategories() {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('name')
  
  if (error) throw error
  return data
}

export async function getSubjects(categoryId = null) {
  let query = supabase
    .from('subjects')
    .select(`
      *,
      category:categories(*)
    `)
    .order('name')
  
  if (categoryId) {
    query = query.eq('category_id', categoryId)
  }
  
  const { data, error } = await query
  if (error) throw error
  return data
}

// File Functions
export async function getFiles(filters = {}) {
  let query = supabase
    .from('files')
    .select(`
      *,
      uploaded_by:profiles!uploaded_by(*),
      subject:subjects(*),
      category:categories(*),
      ratings(rating),
      _count:comments(count)
    `)
    .eq('status', 'approved')
    .order('created_at', { ascending: false })
  
  // Apply filters
  if (filters.category_id) {
    query = query.eq('category_id', filters.category_id)
  }
  
  if (filters.subject_id) {
    query = query.eq('subject_id', filters.subject_id)
  }
  
  if (filters.file_type) {
    query = query.eq('file_type', filters.file_type)
  }
  
  if (filters.search) {
    query = query.textSearch('search_vector', filters.search)
  }
  
  if (filters.limit) {
    query = query.limit(filters.limit)
  }
  
  const { data, error } = await query
  if (error) throw error
  return data
}

export async function getFileById(fileId) {
  const { data, error } = await supabase
    .from('files')
    .select(`
      *,
      uploaded_by:profiles!uploaded_by(*),
      subject:subjects(*),
      category:categories(*),
      ratings(*),
      comments(
        *,
        user:profiles(*)
      )
    `)
    .eq('id', fileId)
    .single()
  
  if (error) throw error
  return data
}

export async function uploadFile(fileData) {
  const { data, error } = await supabase
    .from('files')
    .insert(fileData)
    .select()
    .single()
  
  if (error) throw error
  return data
}

export async function updateFile(fileId, updates) {
  const { data, error } = await supabase
    .from('files')
    .update(updates)
    .eq('id', fileId)
    .select()
    .single()
  
  if (error) throw error
  return data
}

export async function deleteFile(fileId) {
  const { error } = await supabase
    .from('files')
    .delete()
    .eq('id', fileId)
  
  if (error) throw error
}

// Comment Functions
export async function getComments(fileId) {
  const { data, error } = await supabase
    .from('comments')
    .select(`
      *,
      user:profiles(*),
      replies:comments(
        *,
        user:profiles(*)
      )
    `)
    .eq('file_id', fileId)
    .is('parent_id', null)
    .order('created_at', { ascending: true })
  
  if (error) throw error
  return data
}

export async function addComment(commentData) {
  const { data, error } = await supabase
    .from('comments')
    .insert(commentData)
    .select(`
      *,
      user:profiles(*)
    `)
    .single()
  
  if (error) throw error
  return data
}

// Bookmark Functions
export async function getUserBookmarks(userId) {
  const { data, error } = await supabase
    .from('bookmarks')
    .select(`
      *,
      file:files(
        *,
        uploaded_by:profiles!uploaded_by(*),
        subject:subjects(*),
        category:categories(*)
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}

export async function toggleBookmark(userId, fileId) {
  // Check if bookmark exists
  const { data: existing } = await supabase
    .from('bookmarks')
    .select('id')
    .eq('user_id', userId)
    .eq('file_id', fileId)
    .single()
  
  if (existing) {
    // Remove bookmark
    const { error } = await supabase
      .from('bookmarks')
      .delete()
      .eq('user_id', userId)
      .eq('file_id', fileId)
    
    if (error) throw error
    return false
  } else {
    // Add bookmark
    const { error } = await supabase
      .from('bookmarks')
      .insert({ user_id: userId, file_id: fileId })
    
    if (error) throw error
    return true
  }
}

// Rating Functions
export async function addOrUpdateRating(userId, fileId, rating, review = null) {
  const { data, error } = await supabase
    .from('ratings')
    .upsert({
      user_id: userId,
      file_id: fileId,
      rating,
      review
    })
    .select()
    .single()
  
  if (error) throw error
  return data
}

// Admin Functions
export async function getPendingFiles() {
  const { data, error } = await supabaseAdmin
    .from('files')
    .select(`
      *,
      uploaded_by:profiles!uploaded_by(*),
      subject:subjects(*),
      category:categories(*)
    `)
    .eq('status', 'pending')
    .order('created_at', { ascending: true })
  
  if (error) throw error
  return data
}

export async function moderateFile(fileId, status, moderatorId, notes = null) {
  const { data, error } = await supabaseAdmin
    .from('files')
    .update({
      status,
      moderated_by: moderatorId,
      moderated_at: new Date().toISOString(),
      moderation_notes: notes
    })
    .eq('id', fileId)
    .select()
    .single()
  
  if (error) throw error
  return data
}

// Statistics Functions
export async function getFileStats(fileId) {
  const { data, error } = await supabase
    .from('files')
    .select('download_count, view_count')
    .eq('id', fileId)
    .single()
  
  if (error) throw error
  return data
}

export async function recordDownload(userId, fileId, ipAddress, userAgent) {
  // Record download
  const { error: downloadError } = await supabase
    .from('downloads')
    .insert({
      user_id: userId,
      file_id: fileId,
      ip_address: ipAddress,
      user_agent: userAgent
    })
  
  if (downloadError) throw downloadError
  
  // Increment download count
  const { error: countError } = await supabase
    .rpc('increment_download_count', { file_uuid: fileId })
  
  if (countError) throw countError
}

export async function recordView(fileId) {
  const { error } = await supabase
    .rpc('increment_view_count', { file_uuid: fileId })
  
  if (error) throw error
}
