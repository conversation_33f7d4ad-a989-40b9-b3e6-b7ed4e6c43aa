import { supabase } from './supabase'

export async function testDatabaseConnection() {
  try {
    console.log('🔍 Testing Supabase connection...')
    
    // Test 1: Basic connection
    const { data: connectionTest, error: connectionError } = await supabase
      .from('categories')
      .select('count')
      .limit(1)
    
    if (connectionError) {
      console.error('❌ Database connection failed:', connectionError.message)
      return { success: false, error: connectionError.message }
    }
    
    console.log('✅ Database connection successful')
    
    // Test 2: Check if tables exist
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('*')
      .limit(5)
    
    if (categoriesError) {
      console.error('❌ Categories table not found:', categoriesError.message)
      return { success: false, error: 'Categories table not found. Please run the schema.sql file.' }
    }
    
    console.log('✅ Categories table exists with', categories.length, 'entries')
    
    // Test 3: Check subjects table
    const { data: subjects, error: subjectsError } = await supabase
      .from('subjects')
      .select('*')
      .limit(5)
    
    if (subjectsError) {
      console.error('❌ Subjects table not found:', subjectsError.message)
      return { success: false, error: 'Subjects table not found. Please run the schema.sql file.' }
    }
    
    console.log('✅ Subjects table exists with', subjects.length, 'entries')
    
    // Test 4: Check auth configuration
    const { data: authUser, error: authError } = await supabase.auth.getUser()
    console.log('✅ Auth system accessible')
    
    // Test 5: Check storage bucket
    const { data: buckets, error: storageError } = await supabase.storage.listBuckets()
    
    if (storageError) {
      console.warn('⚠️ Storage access limited (this is normal for client-side)')
    } else {
      const notexBucket = buckets.find(bucket => bucket.name === 'notex-files')
      if (notexBucket) {
        console.log('✅ Storage bucket "notex-files" found')
      } else {
        console.warn('⚠️ Storage bucket "notex-files" not found. Please create it manually.')
      }
    }
    
    return {
      success: true,
      data: {
        categories: categories.length,
        subjects: subjects.length,
        authConfigured: true
      }
    }
    
  } catch (error) {
    console.error('❌ Connection test failed:', error.message)
    return { success: false, error: error.message }
  }
}

export async function testAuthFlow() {
  try {
    console.log('🔍 Testing authentication flow...')
    
    // Test sign up (with a test email that won't actually be used)
    const testEmail = `test-${Date.now()}@example.com`
    const testPassword = 'testpassword123'
    
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          full_name: 'Test User'
        }
      }
    })
    
    if (signUpError && !signUpError.message.includes('already registered')) {
      console.error('❌ Auth sign up test failed:', signUpError.message)
      return { success: false, error: signUpError.message }
    }
    
    console.log('✅ Auth sign up flow working')
    
    // Test sign out
    await supabase.auth.signOut()
    console.log('✅ Auth sign out working')
    
    return { success: true }
    
  } catch (error) {
    console.error('❌ Auth test failed:', error.message)
    return { success: false, error: error.message }
  }
}

// Helper function to run all tests
export async function runAllTests() {
  console.log('🚀 Starting Supabase integration tests...\n')
  
  const dbTest = await testDatabaseConnection()
  const authTest = await testAuthFlow()
  
  console.log('\n📊 Test Results:')
  console.log('Database Connection:', dbTest.success ? '✅ PASS' : '❌ FAIL')
  console.log('Authentication Flow:', authTest.success ? '✅ PASS' : '❌ FAIL')
  
  if (!dbTest.success) {
    console.log('\n🔧 Database Issues:')
    console.log('- Error:', dbTest.error)
    console.log('- Solution: Run the schema.sql and seed.sql files in your Supabase SQL Editor')
  }
  
  if (!authTest.success) {
    console.log('\n🔧 Authentication Issues:')
    console.log('- Error:', authTest.error)
    console.log('- Solution: Check your Supabase Auth configuration')
  }
  
  if (dbTest.success && authTest.success) {
    console.log('\n🎉 All tests passed! Your Supabase integration is ready.')
  }
  
  return {
    database: dbTest,
    auth: authTest,
    allPassed: dbTest.success && authTest.success
  }
}
