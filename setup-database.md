# Database Setup Instructions

## Step 4: Set up Database Schema

1. **Go to your Supabase Dashboard**
   - Navigate to https://supabase.com/dashboard
   - Select your `notex-digital-library` project

2. **Open SQL Editor**
   - Click on "SQL Editor" in the left sidebar
   - Click "New Query"

3. **Run the Schema SQL**
   - Copy the entire content from `supabase/schema.sql`
   - Paste it into the SQL editor
   - Click "Run" to execute the schema

4. **Run the Seed Data**
   - Create another new query
   - Copy the entire content from `supabase/seed.sql`
   - Paste it into the SQL editor
   - Click "Run" to populate initial data

## Step 5: Configure Authentication

1. **Go to Authentication Settings**
   - Click "Authentication" in the left sidebar
   - Click "Settings"

2. **Configure Auth Settings**
   - **Site URL**: `http://localhost:3000` (for development)
   - **Redirect URLs**: Add `http://localhost:3000/auth/callback`
   - **Email Templates**: Customize if needed (optional)

3. **Enable Email Auth**
   - Ensure "Enable email confirmations" is checked
   - Set "Confirm email" to your preference (recommended: enabled)

## Step 6: Set up Storage

1. **Go to Storage**
   - Click "Storage" in the left sidebar
   - Click "Create a new bucket"

2. **Create the Files Bucket**
   - **Name**: `notex-files`
   - **Public bucket**: Leave unchecked (we'll use RLS)
   - Click "Create bucket"

3. **Set up Storage Policies**
   - Click on the `notex-files` bucket
   - Go to "Policies" tab
   - Click "New Policy"

4. **Create Upload Policy**
   ```sql
   -- Policy name: "Users can upload files"
   -- Allowed operation: INSERT
   -- Target roles: authenticated
   
   CREATE POLICY "Users can upload files" ON storage.objects
   FOR INSERT TO authenticated
   WITH CHECK (bucket_id = 'notex-files');
   ```

5. **Create Download Policy**
   ```sql
   -- Policy name: "Users can download approved files"
   -- Allowed operation: SELECT
   -- Target roles: authenticated, anon
   
   CREATE POLICY "Users can download approved files" ON storage.objects
   FOR SELECT TO authenticated, anon
   USING (bucket_id = 'notex-files');
   ```

6. **Create Update Policy**
   ```sql
   -- Policy name: "Users can update own files"
   -- Allowed operation: UPDATE
   -- Target roles: authenticated
   
   CREATE POLICY "Users can update own files" ON storage.objects
   FOR UPDATE TO authenticated
   USING (bucket_id = 'notex-files' AND auth.uid()::text = (storage.foldername(name))[1]);
   ```

7. **Create Delete Policy**
   ```sql
   -- Policy name: "Users can delete own files"
   -- Allowed operation: DELETE
   -- Target roles: authenticated
   
   CREATE POLICY "Users can delete own files" ON storage.objects
   FOR DELETE TO authenticated
   USING (bucket_id = 'notex-files' AND auth.uid()::text = (storage.foldername(name))[1]);
   ```

## Step 7: Verify Setup

After completing the above steps, you should have:

- ✅ Database schema with all tables created
- ✅ Row Level Security policies enabled
- ✅ Initial categories and subjects seeded
- ✅ Authentication configured
- ✅ Storage bucket created with policies
- ✅ Environment variables configured

## Next Steps

Once you've completed the database setup, you can test the integration by running:

```bash
npm run dev
```

The application should now be able to:
- Connect to your Supabase database
- Handle user authentication
- Access the storage bucket (when file upload is implemented)

## Troubleshooting

If you encounter issues:

1. **Check Environment Variables**: Ensure all keys are correct and no extra spaces
2. **Verify Database**: Check that all tables were created in the SQL Editor
3. **Check Auth Settings**: Ensure Site URL matches your development URL
4. **Storage Policies**: Verify all storage policies were created successfully

## Testing Database Connection

You can test the database connection by trying to register a new user account through the application interface.
