'use client'

import { useState, useEffect } from 'react'
import { runAllTests } from '@/lib/test-connection'
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from 'lucide-react'

export default function TestConnectionPage() {
  const [testResults, setTestResults] = useState(null)
  const [isRunning, setIsRunning] = useState(false)

  const runTests = async () => {
    setIsRunning(true)
    setTestResults(null)
    
    try {
      const results = await runAllTests()
      setTestResults(results)
    } catch (error) {
      setTestResults({
        database: { success: false, error: error.message },
        auth: { success: false, error: error.message },
        allPassed: false
      })
    } finally {
      setIsRunning(false)
    }
  }

  useEffect(() => {
    runTests()
  }, [])

  const getStatusIcon = (success) => {
    if (success) {
      return <CheckCircle className="h-6 w-6 text-green-500" />
    } else {
      return <XCircle className="h-6 w-6 text-red-500" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Supabase Integration Test
            </h1>
            <p className="text-gray-600">
              This page tests your Supabase configuration and database setup.
            </p>
          </div>

          <div className="flex justify-center mb-8">
            <button
              onClick={runTests}
              disabled={isRunning}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isRunning ? (
                <>
                  <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                  Running Tests...
                </>
              ) : (
                <>
                  <RefreshCw className="h-5 w-5 mr-2" />
                  Run Tests
                </>
              )}
            </button>
          </div>

          {testResults && (
            <div className="space-y-6">
              {/* Overall Status */}
              <div className={`p-4 rounded-lg border ${
                testResults.allPassed 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center">
                  {testResults.allPassed ? (
                    <CheckCircle className="h-8 w-8 text-green-500 mr-3" />
                  ) : (
                    <XCircle className="h-8 w-8 text-red-500 mr-3" />
                  )}
                  <div>
                    <h3 className={`text-lg font-semibold ${
                      testResults.allPassed ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {testResults.allPassed ? 'All Tests Passed!' : 'Some Tests Failed'}
                    </h3>
                    <p className={`text-sm ${
                      testResults.allPassed ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {testResults.allPassed 
                        ? 'Your Supabase integration is working correctly.'
                        : 'Please check the issues below and follow the setup instructions.'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* Individual Test Results */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Database Test */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    {getStatusIcon(testResults.database.success)}
                    <h3 className="text-lg font-semibold text-gray-900 ml-3">
                      Database Connection
                    </h3>
                  </div>
                  
                  {testResults.database.success ? (
                    <div className="space-y-2">
                      <p className="text-sm text-green-700">
                        ✅ Connected to Supabase database
                      </p>
                      <p className="text-sm text-green-700">
                        ✅ Categories table: {testResults.database.data?.categories} entries
                      </p>
                      <p className="text-sm text-green-700">
                        ✅ Subjects table: {testResults.database.data?.subjects} entries
                      </p>
                      <p className="text-sm text-green-700">
                        ✅ Schema and seed data loaded
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <p className="text-sm text-red-700">
                        ❌ Error: {testResults.database.error}
                      </p>
                      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <p className="text-sm text-yellow-800 font-medium">
                          To fix this:
                        </p>
                        <ol className="text-sm text-yellow-700 mt-2 list-decimal list-inside space-y-1">
                          <li>Go to your Supabase SQL Editor</li>
                          <li>Run the content from supabase/schema.sql</li>
                          <li>Run the content from supabase/seed.sql</li>
                          <li>Refresh this page</li>
                        </ol>
                      </div>
                    </div>
                  )}
                </div>

                {/* Auth Test */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    {getStatusIcon(testResults.auth.success)}
                    <h3 className="text-lg font-semibold text-gray-900 ml-3">
                      Authentication
                    </h3>
                  </div>
                  
                  {testResults.auth.success ? (
                    <div className="space-y-2">
                      <p className="text-sm text-green-700">
                        ✅ Auth system accessible
                      </p>
                      <p className="text-sm text-green-700">
                        ✅ Sign up flow working
                      </p>
                      <p className="text-sm text-green-700">
                        ✅ Sign out flow working
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <p className="text-sm text-red-700">
                        ❌ Error: {testResults.auth.error}
                      </p>
                      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <p className="text-sm text-yellow-800 font-medium">
                          To fix this:
                        </p>
                        <ol className="text-sm text-yellow-700 mt-2 list-decimal list-inside space-y-1">
                          <li>Check your environment variables</li>
                          <li>Verify Supabase Auth settings</li>
                          <li>Ensure Site URL is configured</li>
                        </ol>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Next Steps */}
              {testResults.allPassed && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <AlertCircle className="h-6 w-6 text-blue-500 mr-3" />
                    <h3 className="text-lg font-semibold text-blue-900">
                      Next Steps
                    </h3>
                  </div>
                  <div className="space-y-2 text-sm text-blue-700">
                    <p>✅ Your Supabase integration is ready!</p>
                    <p>🚀 You can now:</p>
                    <ul className="list-disc list-inside ml-4 space-y-1">
                      <li>Test user registration and login</li>
                      <li>Set up the storage bucket for file uploads</li>
                      <li>Continue with the file upload system implementation</li>
                    </ul>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Manual Setup Instructions */}
          <div className="mt-12 border-t border-gray-200 pt-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Manual Setup Checklist
            </h2>
            <div className="space-y-3">
              <div className="flex items-center">
                <input type="checkbox" className="h-4 w-4 text-blue-600 rounded" />
                <label className="ml-3 text-sm text-gray-700">
                  Created Supabase project
                </label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" className="h-4 w-4 text-blue-600 rounded" />
                <label className="ml-3 text-sm text-gray-700">
                  Updated .env.local with project credentials
                </label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" className="h-4 w-4 text-blue-600 rounded" />
                <label className="ml-3 text-sm text-gray-700">
                  Ran schema.sql in Supabase SQL Editor
                </label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" className="h-4 w-4 text-blue-600 rounded" />
                <label className="ml-3 text-sm text-gray-700">
                  Ran seed.sql in Supabase SQL Editor
                </label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" className="h-4 w-4 text-blue-600 rounded" />
                <label className="ml-3 text-sm text-gray-700">
                  Configured Authentication settings
                </label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" className="h-4 w-4 text-blue-600 rounded" />
                <label className="ml-3 text-sm text-gray-700">
                  Created "notex-files" storage bucket
                </label>
              </div>
              <div className="flex items-center">
                <input type="checkbox" className="h-4 w-4 text-blue-600 rounded" />
                <label className="ml-3 text-sm text-gray-700">
                  Set up storage bucket policies
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
