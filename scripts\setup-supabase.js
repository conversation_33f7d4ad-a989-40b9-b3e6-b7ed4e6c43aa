#!/usr/bin/env node

/**
 * Supabase Setup Verification Script
 * 
 * This script helps verify that your Supabase configuration is correct
 * and provides guidance for any missing setup steps.
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 NoteX Supabase Setup Verification\n')

// Check if .env.local exists and has required variables
function checkEnvironmentVariables() {
  console.log('1. Checking environment variables...')
  
  const envPath = path.join(process.cwd(), '.env.local')
  
  if (!fs.existsSync(envPath)) {
    console.log('❌ .env.local file not found')
    console.log('   Create .env.local file with your Supabase credentials\n')
    return false
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8')
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ]
  
  const missingVars = []
  
  requiredVars.forEach(varName => {
    if (!envContent.includes(varName) || envContent.includes(`${varName}=your_`)) {
      missingVars.push(varName)
    }
  })
  
  if (missingVars.length > 0) {
    console.log('❌ Missing or incomplete environment variables:')
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`)
    })
    console.log('   Update your .env.local file with actual Supabase credentials\n')
    return false
  }
  
  console.log('✅ Environment variables configured\n')
  return true
}

// Check if schema and seed files exist
function checkSQLFiles() {
  console.log('2. Checking SQL files...')
  
  const schemaPath = path.join(process.cwd(), 'supabase', 'schema.sql')
  const seedPath = path.join(process.cwd(), 'supabase', 'seed.sql')
  
  if (!fs.existsSync(schemaPath)) {
    console.log('❌ supabase/schema.sql not found')
    return false
  }
  
  if (!fs.existsSync(seedPath)) {
    console.log('❌ supabase/seed.sql not found')
    return false
  }
  
  console.log('✅ SQL files found\n')
  return true
}

// Display setup instructions
function displaySetupInstructions() {
  console.log('📋 SETUP INSTRUCTIONS\n')
  
  console.log('Step 1: Create Supabase Project')
  console.log('- Go to https://supabase.com')
  console.log('- Create a new project named "notex-digital-library"')
  console.log('- Wait for project initialization\n')
  
  console.log('Step 2: Get Project Credentials')
  console.log('- Go to Settings → API in your Supabase dashboard')
  console.log('- Copy Project URL, anon key, and service_role key')
  console.log('- Update your .env.local file with these values\n')
  
  console.log('Step 3: Set up Database')
  console.log('- Go to SQL Editor in your Supabase dashboard')
  console.log('- Create a new query')
  console.log('- Copy and paste the entire content from supabase/schema.sql')
  console.log('- Click "Run" to execute')
  console.log('- Create another new query')
  console.log('- Copy and paste the entire content from supabase/seed.sql')
  console.log('- Click "Run" to execute\n')
  
  console.log('Step 4: Configure Authentication')
  console.log('- Go to Authentication → Settings')
  console.log('- Set Site URL to: http://localhost:3000')
  console.log('- Add Redirect URL: http://localhost:3000/auth/callback\n')
  
  console.log('Step 5: Set up Storage')
  console.log('- Go to Storage in your Supabase dashboard')
  console.log('- Create a new bucket named "notex-files"')
  console.log('- Keep it private (uncheck "Public bucket")')
  console.log('- Set up the storage policies as described in setup-database.md\n')
  
  console.log('Step 6: Test Integration')
  console.log('- Run: npm run dev')
  console.log('- Visit: http://localhost:3000/test-connection')
  console.log('- Verify all tests pass\n')
}

// Display storage policies
function displayStoragePolicies() {
  console.log('🗄️  STORAGE POLICIES\n')
  console.log('Run these in your Supabase SQL Editor after creating the storage bucket:\n')
  
  console.log('-- Allow authenticated users to upload files')
  console.log(`CREATE POLICY "Users can upload files" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'notex-files');

-- Allow users to download files
CREATE POLICY "Users can download files" ON storage.objects
FOR SELECT TO authenticated, anon
USING (bucket_id = 'notex-files');

-- Allow users to update their own files
CREATE POLICY "Users can update own files" ON storage.objects
FOR UPDATE TO authenticated
USING (bucket_id = 'notex-files' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Allow users to delete their own files
CREATE POLICY "Users can delete own files" ON storage.objects
FOR DELETE TO authenticated
USING (bucket_id = 'notex-files' AND auth.uid()::text = (storage.foldername(name))[1]);
`)
}

// Main execution
function main() {
  const envOk = checkEnvironmentVariables()
  const sqlOk = checkSQLFiles()
  
  if (envOk && sqlOk) {
    console.log('🎉 All files are in place!')
    console.log('Now follow the setup instructions to configure your Supabase project.\n')
  } else {
    console.log('⚠️  Some setup steps are missing. Please complete them first.\n')
  }
  
  displaySetupInstructions()
  displayStoragePolicies()
  
  console.log('💡 HELPFUL TIPS:')
  console.log('- Keep your Supabase dashboard open during setup')
  console.log('- Test each step before moving to the next')
  console.log('- Use the test page at /test-connection to verify setup')
  console.log('- Check the browser console for detailed error messages\n')
  
  console.log('🔗 USEFUL LINKS:')
  console.log('- Supabase Dashboard: https://supabase.com/dashboard')
  console.log('- Documentation: https://supabase.com/docs')
  console.log('- Test Page: http://localhost:3000/test-connection (after npm run dev)\n')
}

main()
