-- Insert default categories
INSERT INTO public.categories (name, description, slug, color) VALUES
('Computer Science', 'Programming, algorithms, software engineering', 'computer-science', '#3B82F6'),
('Mathematics', 'Calculus, algebra, statistics, discrete math', 'mathematics', '#10B981'),
('Physics', 'Classical mechanics, quantum physics, thermodynamics', 'physics', '#F59E0B'),
('Chemistry', 'Organic, inorganic, physical chemistry', 'chemistry', '#EF4444'),
('Biology', 'Molecular biology, genetics, ecology', 'biology', '#8B5CF6'),
('Engineering', 'Mechanical, electrical, civil engineering', 'engineering', '#F97316'),
('Business', 'Management, finance, marketing, economics', 'business', '#06B6D4'),
('Literature', 'English literature, creative writing, linguistics', 'literature', '#EC4899'),
('History', 'World history, political science, sociology', 'history', '#84CC16'),
('General', 'Miscellaneous academic materials', 'general', '#6B7280');

-- Insert sample subjects for Computer Science
INSERT INTO public.subjects (name, code, description, category_id) VALUES
('Data Structures and Algorithms', 'CS201', 'Fundamental data structures and algorithmic techniques', 
    (SELECT id FROM public.categories WHERE slug = 'computer-science')),
('Database Systems', 'CS301', 'Relational databases, SQL, database design', 
    (SELECT id FROM public.categories WHERE slug = 'computer-science')),
('Web Development', 'CS350', 'Frontend and backend web development technologies', 
    (SELECT id FROM public.categories WHERE slug = 'computer-science')),
('Machine Learning', 'CS401', 'Introduction to machine learning algorithms and applications', 
    (SELECT id FROM public.categories WHERE slug = 'computer-science')),
('Software Engineering', 'CS302', 'Software development lifecycle, design patterns, testing', 
    (SELECT id FROM public.categories WHERE slug = 'computer-science'));

-- Insert sample subjects for Mathematics
INSERT INTO public.subjects (name, code, description, category_id) VALUES
('Calculus I', 'MATH101', 'Limits, derivatives, and basic integration', 
    (SELECT id FROM public.categories WHERE slug = 'mathematics')),
('Linear Algebra', 'MATH201', 'Vector spaces, matrices, eigenvalues and eigenvectors', 
    (SELECT id FROM public.categories WHERE slug = 'mathematics')),
('Statistics', 'MATH301', 'Probability theory, statistical inference, hypothesis testing', 
    (SELECT id FROM public.categories WHERE slug = 'mathematics')),
('Discrete Mathematics', 'MATH250', 'Logic, set theory, graph theory, combinatorics', 
    (SELECT id FROM public.categories WHERE slug = 'mathematics'));

-- Insert sample subjects for Physics
INSERT INTO public.subjects (name, code, description, category_id) VALUES
('Classical Mechanics', 'PHYS101', 'Newtonian mechanics, energy, momentum', 
    (SELECT id FROM public.categories WHERE slug = 'physics')),
('Electromagnetism', 'PHYS201', 'Electric and magnetic fields, Maxwell equations', 
    (SELECT id FROM public.categories WHERE slug = 'physics')),
('Quantum Physics', 'PHYS301', 'Wave-particle duality, Schrödinger equation, quantum mechanics', 
    (SELECT id FROM public.categories WHERE slug = 'physics'));

-- Insert sample subjects for other categories
INSERT INTO public.subjects (name, code, description, category_id) VALUES
('Organic Chemistry', 'CHEM201', 'Structure and reactions of organic compounds', 
    (SELECT id FROM public.categories WHERE slug = 'chemistry')),
('Cell Biology', 'BIO101', 'Cell structure, function, and molecular processes', 
    (SELECT id FROM public.categories WHERE slug = 'biology')),
('Thermodynamics', 'ENGR201', 'Heat transfer, energy conversion, thermodynamic cycles', 
    (SELECT id FROM public.categories WHERE slug = 'engineering')),
('Microeconomics', 'ECON101', 'Supply and demand, market structures, consumer behavior', 
    (SELECT id FROM public.categories WHERE slug = 'business')),
('World Literature', 'LIT201', 'Major works of world literature and literary analysis', 
    (SELECT id FROM public.categories WHERE slug = 'literature')),
('Modern History', 'HIST301', 'World history from 1800 to present', 
    (SELECT id FROM public.categories WHERE slug = 'history'));

-- Create storage bucket for files (this would typically be done via Supabase dashboard)
-- INSERT INTO storage.buckets (id, name, public) VALUES ('notex-files', 'notex-files', false);
